# Knihy – kontrola nahrání knih jízd (CLI)

Skript v Pythonu, který:

- Načte/pož<PERSON>dá o přihlaš<PERSON><PERSON>í <PERSON> (uživatel, heslo) a nabídne jejich uložení do Windows Credential Manageru (keyring) nebo do lokálního souboru `.knihy_credentials.json`.
- Připojí se na danou stránku přes HTTP Basic Authentication.
- Z HTML vytáhne seznam Excel souborů (.xls/.xlsx), SPZ (7–8 znaků) a datum nahrání.
- Vygeneruje Excel report `kontrola_knih_jizd.xlsx` se sloupci: `SPZ`, `Da<PERSON> nahr<PERSON>`, `Splněno` (zvýraznění barvami podle ±5 dní od konce měsíce).

> Pozn.: Pokud web nepoužívá Basic Auth (ale formulář/NTLM), tento skript se nepřihlásí. Pro HTTPS s nevalidním certifikátem použijte přepínač `--insecure` (verify=False).

## Instalace

```powershell
pip install -r requirements.txt
```

Balíčky: `requests`, `beautifulsoup4`, `lxml`, `openpyxl`, `keyring`, `python-dateutil`.
Pro NTLM navíc: `requests-ntlm` (už je v requirements.txt).

## Spuštění (PowerShell)

```powershell
python .\knihy.py --url "https://intranet.img-management.cz/Porady/KNIHY%20JZD/"  # příklad základní URL
```

První spuštění se interaktivně zeptá na jméno a heslo a nabídne jejich uložení. Při dalších spuštěních se použijí uložené údaje.

Volby:

- `--url`, `-u` – URL stránky, ze které se čte seznam souborů
- `--insecure` – vypne SSL ověřování certifikátu (verify=False) – použijte pouze, pokud je certifikát neplatný
- `--output`, `-o` – název výsledného Excel souboru (výchozí `kontrola_knih_jizd.xlsx`)
- `--user` – přepíše uložené uživatelské jméno
- `--ask-password` – vynutí dotaz na heslo (ignoruje uložené)
- `--forget` – smaže uložené přihlašovací údaje a skončí
- `--debug` – vypíše ladicí informace (HTTP kód, WWW-Authenticate)
- `--auth basic|ntlm` – typ autentizace (výchozí `basic`). Pro SharePoint/NTLM použij `--auth ntlm` a uveď účet ve tvaru `DOMENA\\uzivatel` nebo `uzivatel@domena`.

## Co skript dělá

1. Získá přihlašovací údaje (keyring/soubor/uživatel).
2. Dotáže se na zadanou `--url` přes HTTP Basic Auth.
3. Z HTML vytáhne odkazy na `.xls`/`.xlsx`, odhadne SPZ z názvu souboru a datum nahrání (z okolního textu/elementů).
4. Vytvoří Excel s barvami: zelená pro „ANO“ (datum v rozmezí ±5 dní okolo konce měsíce), červená pro „NE“.

## Poznámky

- Pokud `keyring` není dostupný, budou údaje uloženy do lokálního souboru `.knihy_credentials.json` ve čitelné podobě.
- Extrakce datumu je heuristická; pokud stránka neposkytuje datum poblíž odkazu, může zůstat prázdné a řádek bude „NE“.

### Řešení chyby 401 (Neautorizováno)

- Ověřte, že cíl skutečně používá HTTP Basic Auth, ne formulářové přihlášení nebo NTLM/SharePoint.
- Zkuste vynutit nové zadání údajů:
	```powershell
	python .\knihy.py --url "..." --user "VASE_UZ_JMENO" --ask-password --debug
	```
- Pokud chcete smazat uložené údaje:
	```powershell
	python .\knihy.py --forget
	```
- Pokud server nepodporuje Basic, kontaktujte správce nebo dejte vědět a upravíme skript (např. NTLM přes `requests-ntlm`).
	Pro SharePoint/NTLM zkuste:
	```powershell
	python .\knihy.py --url "..." --auth ntlm --user "DOMENA\\UZIVATEL" --ask-password --debug
	```
