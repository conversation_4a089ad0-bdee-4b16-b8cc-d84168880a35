"""
Knihy – kontrola nahrání knih jízd

Po spuštění:
- <PERSON>ísk<PERSON> přihlaš<PERSON><PERSON> (uživatel, heslo) – buď z bezpečného <PERSON> (Windows Credential Manager přes `keyring`),
  nebo z lokálního souboru `.knihy_credentials.json`. Pokud nej<PERSON>, interaktivně se zeptá a nabídne uložení.
- Připojí se na zadanou URL přes HTTP Basic Authentication.
- Z HTML stránky vytáhne seznam Excel souborů (.xls/.xlsx), odhadne SPZ (7–8 znaků) a datum nahrání.
- Vygeneruje Excel report `Kontrola_knihy jízd.xlsx` se sloupci: SPZ, Datum nahrání, Splněno (ANO/NE, barvy).

Pozn.: Web reálně může vyžadovat jiné přihlášení než Basic Auth (např. form<PERSON>, NTLM apod.). Tento skript řeší
explicitně Basic Auth dle zadání. Pro HTTPS s nevalidním certifikátem lze použít `--insecure` (verify=False).
"""

from __future__ import annotations

import base64
import json
import os
import re
import sys
from dataclasses import dataclass
from datetime import datetime, timedelta, date
from pathlib import Path
from typing import Iterable, List, Optional, Tuple

import requests
from bs4 import BeautifulSoup

# Volitelné: lepší HTML parser a flexibilnější parsování dat
try:
	import lxml  # noqa: F401
	BS_PARSER = "lxml"
except Exception:
	BS_PARSER = "html.parser"

try:
	import keyring  # type: ignore
except Exception:  # keyring není povinné
	keyring = None  # type: ignore

try:
	from dateutil import parser as dateparser  # type: ignore
except Exception:
	dateparser = None  # type: ignore

# NTLM (volitelně)
try:
	from requests_ntlm import HttpNtlmAuth  # type: ignore
except Exception:
	HttpNtlmAuth = None  # type: ignore

from openpyxl import Workbook
from openpyxl.styles import PatternFill, Font, Alignment


# Selenium (volitelně) – pro Google login / prohlížečovou automatizaci
try:
    from selenium import webdriver  # type: ignore
    from selenium.webdriver.chrome.service import Service as ChromeService  # type: ignore
    from selenium.webdriver.chrome.options import Options as ChromeOptions  # type: ignore
    from webdriver_manager.chrome import ChromeDriverManager  # type: ignore
    from selenium.webdriver.common.by import By  # type: ignore
    from selenium.webdriver.support.ui import WebDriverWait  # type: ignore
    from selenium.webdriver.support import expected_conditions as EC  # type: ignore
except Exception:
    webdriver = None  # type: ignore
    ChromeService = None  # type: ignore
    ChromeOptions = None  # type: ignore
    ChromeDriverManager = None  # type: ignore
    By = None  # type: ignore
    WebDriverWait = None  # type: ignore
    EC = None  # type: ignore


# ------------------------------ Konfigurace ------------------------------

SERVICE_NAME = "knihy_app"
CREDENTIALS_FILE = Path(__file__).with_name(".knihy_credentials.json")
DEFAULT_URL = (
	"https://intranet.img-management.cz/Porady/KNIHY%20JZD/Forms/AllItems.aspx?&&p_SortBehavior=0&p_FileLeafRef=9AL%206014%20%2d%202025%2exls&&PageFirstRow=1&&View={DD08046D-A508-4D0B-BA78-829B558AEBC1}"
)


# ------------------------------ Datové modely ------------------------------

@dataclass
class FileRecord:
	spz: str
	uploaded_at: Optional[datetime]


# ------------------------------ Užitečné funkce ------------------------------

def prompt(text: str) -> str:
	try:
		return input(text)
	except EOFError:
		return ""


def load_saved_credentials() -> Optional[Tuple[str, str]]:
	"""Zkusí načíst uložené přihlašovací údaje.

	Preferuje keyring, ale potřebuje znát uživatelské jméno (je uloženo v JSON s metadaty).
	Pokud keyring není k dispozici, použije čistě JSON.
	"""
	if not CREDENTIALS_FILE.exists():
		return None
	try:
		data = json.loads(CREDENTIALS_FILE.read_text(encoding="utf-8"))
		storage = data.get("storage", "file")
		username = data.get("username")
		if not username:
			return None
		if storage == "keyring" and keyring is not None:
			pwd = keyring.get_password(SERVICE_NAME, username)
			if pwd:
				return username, pwd
			return None
		# Fallback: soubor obsahuje i heslo
		if storage == "file":
			password = data.get("password")
			if password:
				return username, password
	except Exception:
		return None
	return None


def save_credentials(username: str, password: str, prefer_keyring: bool = True) -> None:
	"""Uloží přihlašovací údaje buď do Windows Credential Manageru (keyring),
	nebo do lokálního JSON souboru (pokud keyring není dostupný)."""
	if prefer_keyring and keyring is not None:
		try:
			keyring.set_password(SERVICE_NAME, username, password)
			CREDENTIALS_FILE.write_text(
				json.dumps({"storage": "keyring", "username": username}, ensure_ascii=False, indent=2),
				encoding="utf-8",
			)
			return
		except Exception:
			# Pokud keyring selže, spadneme na soubor
			pass

	# Uložit přímo do souboru (v čitelné podobě)
	CREDENTIALS_FILE.write_text(
		json.dumps(
			{"storage": "file", "username": username, "password": password},
			ensure_ascii=False,
			indent=2,
		),
		encoding="utf-8",
	)


def forget_credentials(username_hint: Optional[str] = None) -> None:
	"""Smaže uložené přihlašovací údaje (keyring i soubor)."""
	# Nejprve zkusit přečíst metadata ze souboru
	stored_username = None
	storage = None
	if CREDENTIALS_FILE.exists():
		try:
			data = json.loads(CREDENTIALS_FILE.read_text(encoding="utf-8"))
			stored_username = data.get("username")
			storage = data.get("storage")
		except Exception:
			pass

	# Volitelně přepíšeme hintem
	if username_hint:
		stored_username = username_hint

	# Smazat z keyringu
	if keyring is not None and stored_username:
		try:
			keyring.delete_password(SERVICE_NAME, stored_username)
		except Exception:
			pass

	# Smazat soubor
	if CREDENTIALS_FILE.exists():
		try:
			CREDENTIALS_FILE.unlink()
		except Exception:
			pass


def get_credentials(username_override: Optional[str] = None, force_prompt_password: bool = False) -> Tuple[str, str]:
	"""Vrátí (username, password). Pokud nejsou uložené, interaktivně se zeptá
	a nabídne uložení (ano/ne)."""
	# 1) Zkusit načíst uložené
	if not username_override and not force_prompt_password:
		saved = load_saved_credentials()
		if saved:
			# Pokud je uložené uživatelské jméno a je stejné jako override (pokud zadán), použij uložené
			if username_override is None or saved[0] == username_override:
				return saved

	# 2) Interaktivně se zeptat
	print("Zadejte přihlašovací údaje k webu (HTTP Basic Auth):")
	if username_override:
		print(f"Uživatelské jméno: {username_override}")
		username = username_override.strip()
	else:
		username = prompt("Uživatelské jméno: ").strip()
	# Heslo nenačítáme přes getpass kvůli UX ve VS Code terminálu na Windows,
	# ale lze snadno zaměnit: from getpass import getpass; getpass("Heslo: ")
	password = prompt("Heslo: ")

	remember = prompt("Chcete údaje uložit pro příště? (ano/ne): ").strip().lower()
	if remember in {"a", "ano", "y", "yes"}:
		prefer_keyring = True
		save_credentials(username, password, prefer_keyring=prefer_keyring)
		print("Údaje uloženy.")
	else:
		print("Údaje nebudou uloženy.")
	return username, password


def build_basic_auth_header(username: str, password: str) -> str:
	token = base64.b64encode(f"{username}:{password}".encode("utf-8")).decode("ascii")
	return f"Basic {token}"


def fetch_html(
	url: str,
	headers: dict,
	verify: bool = True,
	timeout: int = 30,
	debug: bool = False,
	auth=None,
) -> str:
	"""Stáhne HTML stránku. Podporuje Basic (přes headers) i NTLM (přes auth objekt)."""
	try:
		resp = requests.get(url, headers=headers, verify=verify, timeout=timeout, auth=auth)
	except requests.exceptions.SSLError as e:
		raise RuntimeError(
			"Chyba SSL. Pokud server používá neplatný certifikát, spusťte s --insecure (verify=False)."
		) from e
	except requests.RequestException as e:
		raise RuntimeError(f"Chyba při spojení: {e}") from e

	if debug:
		print(f"DEBUG: HTTP {resp.status_code}")
		try:
			www = resp.headers.get("WWW-Authenticate")
			if www:
				print(f"DEBUG: WWW-Authenticate: {www}")
				w = www.lower()
				if ("ntlm" in w or "negotiate" in w) and "basic" not in w:
					print("DEBUG: Server inzeruje NTLM/Negotiate bez Basic – zkuste --auth ntlm")
		except Exception:
			pass

	if resp.status_code == 401:
		hint = (
			"Neautorizováno (401) – zkontrolujte uživatelské jméno a heslo. "
			"Pokud web nepoužívá Basic Auth (nebo vyžaduje NTLM/SharePoint), použijte --auth ntlm."
		)
		raise RuntimeError(hint)
	if resp.status_code == 403:
		raise RuntimeError("Zakázáno (403) – účet nemá přístup nebo je přihlášení blokováno.")
	if resp.status_code >= 400:
		raise RuntimeError(f"HTTP chyba {resp.status_code}: {resp.text[:200]}")

	return resp.text


def ensure_selenium_available() -> None:
    """Zkontroluje, zda jsou nainstalovány Selenium závislosti."""
    if (
        webdriver is None
        or ChromeDriverManager is None
        or ChromeOptions is None
        or ChromeService is None
        or By is None
        or WebDriverWait is None
        or EC is None
    ):
        raise RuntimeError(
            "Selenium není k dispozici. Nainstalujte 'selenium' a 'webdriver-manager' (pip install selenium webdriver-manager)."
        )


def fetch_html_selenium(
    url: str,
    username: Optional[str] = None,
    password: Optional[str] = None,
    headless: bool = False,
    persist: bool = True,
    timeout: int = 90,
    debug: bool = False,
) -> str:
    """Načte HTML stránky přes skutečný prohlížeč (Chrome) pomocí Selenium.

    - Pokud jsou zadány přihlašovací údaje, pokusí se je vyplnit do Google loginu (pokud se objeví).
    - Pokud je aktivní perzistence, vytvoří/užije lokální profil, takže po prvním přihlášení zůstane relace zachována.
    """
    ensure_selenium_available()

    options = ChromeOptions()
    if headless:
        # moderní headless režim
        options.add_argument("--headless=new")
    options.add_argument("--disable-gpu")
    options.add_argument("--no-sandbox")
    options.add_argument("--window-size=1280,900")

    if persist:
        profile_dir = Path(__file__).with_name(".selenium_profile").resolve()
        options.add_argument(f"--user-data-dir={str(profile_dir)}")

    service = ChromeService(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=options)
    driver.set_page_load_timeout(timeout)

    try:
        driver.get(url)

        # Pokus o automatické vyplnění Google přihlášení (nevadí, když nevyjde)
        if username and password:
            try:
                # Email (Identifier)
                WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "identifierId")))
                email_input = driver.find_element(By.ID, "identifierId")
                try:
                    email_input.clear()
                except Exception:
                    pass
                email_input.send_keys(username)
                next_btn = driver.find_element(By.ID, "identifierNext")
                next_btn.click()

                # Heslo
                WebDriverWait(driver, 15).until(EC.presence_of_element_located((By.NAME, "Passwd")))
                pwd_input = driver.find_element(By.NAME, "Passwd")
                try:
                    pwd_input.clear()
                except Exception:
                    pass
                pwd_input.send_keys(password)
                pwd_next = driver.find_element(By.ID, "passwordNext")
                pwd_next.click()
            except Exception:
                # Flow se nenačetl (už přihlášen / jiný IdP). Pokračujeme.
                if debug:
                    print("DEBUG: Automatické vyplnění Google přihlášení se nepodařilo (pravděpodobně již přihlášen).")

        # Po přihlášení/načtení stránky počkáme na odkazy na Excel soubory (pokud existují)
        try:
            WebDriverWait(driver, 30).until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        "//a[contains(translate(@href, 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), '.xls')]",
                    )
                )
            )
        except Exception:
            # Nevadí, stránka může mít jiný layout; zkusíme i tak vzít page_source
            pass

        return driver.page_source
    finally:
        driver.quit()




DATE_REGEXPS = [
	# 31.12.2025, 1.1.2025
	re.compile(r"\b(\d{1,2})[.](\d{1,2})[.](\d{4})\b"),
	# 2025-12-31
	re.compile(r"\b(\d{4})-(\d{2})-(\d{2})\b"),
	# 31/12/2025
	re.compile(r"\b(\d{1,2})/(\d{1,2})/(\d{4})\b"),
]


def try_parse_date(text: str) -> Optional[datetime]:
	text = text.strip()
	if not text:
		return None
	# 1) Zkusíme dateutil, pokud je k dispozici
	if dateparser is not None:
		try:
			dt = dateparser.parse(text, dayfirst=True, yearfirst=False)
			if dt is not None:
				return dt
		except Exception:
			pass
	# 2) Manuální formáty
	for rx in DATE_REGEXPS:
		m = rx.search(text)
		if not m:
			continue
		try:
			if rx is DATE_REGEXPS[0]:  # DD.MM.YYYY
				d, mth, y = map(int, m.groups())
				return datetime(y, mth, d)
			if rx is DATE_REGEXPS[1]:  # YYYY-MM-DD
				y, mth, d = map(int, m.groups())
				return datetime(y, mth, d)
			if rx is DATE_REGEXPS[2]:  # DD/MM/YYYY
				d, mth, y = map(int, m.groups())
				return datetime(y, mth, d)
		except Exception:
			continue
	return None


def extract_spz_from_filename(name: str) -> str:
	"""Vytáhne SPZ jako 7–8 alfanumerických znaků. Pokud nenalezne, vrátí základ jména."""
	base = os.path.splitext(os.path.basename(name))[0]
	up = re.sub(r"\s+", "", base.upper())
	m = re.search(r"[A-Z0-9]{7,8}", up)
	return m.group(0) if m else base


def find_date_near(node) -> Optional[datetime]:
	"""Najde datum v okolí prvku odkazu (stejný řádek/tabulka, sourozenci)."""
	# 1) time datetime atribut
	time_el = node.find_next("time") if hasattr(node, "find_next") else None
	if time_el is not None:
		dt_attr = (time_el.get("datetime") or time_el.get("title") or time_el.get_text(" ")).strip()
		dt = try_parse_date(dt_attr)
		if dt:
			return dt

	# 2) Prohledat text v rámci stejného řádku (tr)
	tr = node.find_parent("tr")
	if tr is not None:
		text = tr.get_text(" ").strip()
		dt = try_parse_date(text)
		if dt:
			return dt

	# 3) Sourozenci
	parent = node.parent
	if parent is not None:
		text = parent.get_text(" ").strip()
		dt = try_parse_date(text)
		if dt:
			return dt

	# 4) fallback: globálně nejbližší text
	text = node.get_text(" ").strip()
	dt = try_parse_date(text)
	return dt


def parse_files_from_html(html: str) -> List[FileRecord]:
	"""Z HTML vytáhne seznam excel souborů (.xls/.xlsx) a odhadne datum nahrání."""
	soup = BeautifulSoup(html, BS_PARSER)
	records: List[FileRecord] = []

	for a in soup.find_all("a", href=True):
		href = a["href"]
		if not href:
			continue
		href_lower = href.lower()
		if not (href_lower.endswith(".xls") or href_lower.endswith(".xlsx") or ".xls?" in href_lower or ".xlsx?" in href_lower):
			continue

		filename = a.get_text(strip=True) or os.path.basename(href)
		spz = extract_spz_from_filename(filename)
		dt = None

		# Zkusit datum v okolí
		try:
			dt = find_date_near(a)
		except Exception:
			dt = None

		records.append(FileRecord(spz=spz, uploaded_at=dt))

	# Odstranit duplicitní SPZ (ponechat poslední výskyt) – volitelně
	unique: dict[str, FileRecord] = {}
	for r in records:
		unique[r.spz] = r
	return list(unique.values())


def fetch_files(
	url: str,
	username: str,
	password: str,
	verify: bool = True,
	debug: bool = False,
	auth_mode: str = "basic",
	selenium_headless: bool = False,
	selenium_persist: bool = True,
) -> List[FileRecord]:
	"""Stáhne HTML a vrátí záznamy souborů."""
	headers = {"User-Agent": "knihy-script/1.0"}
	auth_obj = None
	if auth_mode == "basic":
		headers["Authorization"] = build_basic_auth_header(username, password)
		html = fetch_html(url, headers, verify=verify, debug=debug, auth=auth_obj)
	elif auth_mode == "ntlm":
		if HttpNtlmAuth is None:
			raise RuntimeError(
				"Pro NTLM je nutné nainstalovat 'requests-ntlm' (pip install requests-ntlm)."
			)
		# Uživatel může zadat DOMENA\uzivatel nebo uzivatel@domena
		auth_obj = HttpNtlmAuth(username, password)
		html = fetch_html(url, headers, verify=verify, debug=debug, auth=auth_obj)
	elif auth_mode == "selenium":
		# Vyžaduje balíčky selenium + webdriver-manager
		html = fetch_html_selenium(
			url,
			username=username,
			password=password,
			headless=selenium_headless,
			persist=selenium_persist,
			debug=debug,
		)
	else:
		raise RuntimeError(f"Neznámý auth mód: {auth_mode}")

	return parse_files_from_html(html)


def is_within_eom_window(d: date, window_days: int = 5) -> bool:
	"""Vrátí True, pokud je datum v intervalu ±window_days okolo konce měsíce
	(včetně hran). Např. pro červenec (31. 7.) s window_days=5 je to 26. 7.–5. 8.
	"""
	# Poslední den měsíce
	if d.month == 12:
		next_month_first = date(d.year + 1, 1, 1)
	else:
		next_month_first = date(d.year, d.month + 1, 1)
	last_day = next_month_first - timedelta(days=1)

	start = last_day - timedelta(days=window_days)
	end = last_day + timedelta(days=window_days)
	return start <= d <= end




def last_day_of_month(d: date) -> date:
    """Vrátí poslední den měsíce pro zadané datum."""
    if d.month == 12:
        return date(d.year, 12, 31)
    return (date(d.year, d.month + 1, 1) - timedelta(days=1))


def prev_month_last_day(today: date) -> date:
    """Vrátí poslední den předchozího měsíce vzhledem k dnešku (spuštění kontroly)."""
    if today.month == 1:
        prev = date(today.year - 1, 12, 1)
    else:
        prev = date(today.year, today.month - 1, 1)
    return last_day_of_month(prev)


def is_within_window_around(anchor_last_day: date, d: date, window_days: int = 5) -> bool:
    """Vrátí True, pokud datum d spadá do intervalu ±window_days okolo anchor_last_day.

    Příklad: pokud anchor_last_day = 31.7.2025 a window_days=5, pak platí 26.7.–5.8.
    """
    start = anchor_last_day - timedelta(days=window_days)
    end = anchor_last_day + timedelta(days=window_days)
    return start <= d <= end

def generate_report(records: List[FileRecord], output_path: Path) -> None:
	"""Vytvoří Excel soubor s výsledky a zvýrazněním Splněno ANO/NE."""
	wb = Workbook()
	ws = wb.active
	ws.title = "Kontrola"

	# Hlavička
	headers = ["SPZ", "Datum nahrání", "Splněno"]
	ws.append(headers)
	bold = Font(bold=True)
	for col in range(1, len(headers) + 1):
		cell = ws.cell(row=1, column=col)
		cell.font = bold

	green_fill = PatternFill(start_color="C6EFCE", end_color="C6EFCE", fill_type="solid")
	red_fill = PatternFill(start_color="FFC7CE", end_color="FFC7CE", fill_type="solid")
	center = Alignment(horizontal="center")

	for rec in records:
		dt = rec.uploaded_at
		if dt is None:
			splneno = "NE"
			date_display = ""
			fill = red_fill
		else:
			d = dt.date()
			ok = is_within_window_around(prev_month_last_day(date.today()), d, window_days=5)
			splneno = "ANO" if ok else "NE"
			fill = green_fill if ok else red_fill
			date_display = d.strftime("%d.%m.%Y")

		row_idx = ws.max_row + 1
		ws.append([rec.spz, date_display, splneno])
		c = ws.cell(row=row_idx, column=3)
		c.fill = fill
		c.alignment = center

	# Jednoduché šířky sloupců
	ws.column_dimensions["A"].width = 18
	ws.column_dimensions["B"].width = 18
	ws.column_dimensions["C"].width = 12

	wb.save(output_path)


# ------------------------------ Hlavní běh ------------------------------

def main(argv: List[str]) -> int:
	import argparse
	parser = argparse.ArgumentParser(description="Kontrola nahrání knih jízd z webu (Basic/NTLM/Selenium)")
	parser.add_argument("--url", "-u", default=DEFAULT_URL, help="URL stránky se soubory (.xls/.xlsx)")
	parser.add_argument("--insecure", action="store_true", help="Nepoužívat ověřování certifikátu (verify=False)")
	parser.add_argument("--output", "-o", default="Kontrola_knihy jízd.xlsx", help="Název výstupního Excel souboru")
	parser.add_argument("--user", help="Přepsat uložené uživatelské jméno a přihlásit se pod ním")
	parser.add_argument("--ask-password", action="store_true", help="Vynutit dotaz na heslo (ignorovat uložené)")
	parser.add_argument("--forget", action="store_true", help="Smazat uložené přihlašovací údaje a skončit")
	parser.add_argument("--debug", action="store_true", help="Vypsat ladicí informace (HTTP kód, hlavičky)")
	parser.add_argument("--auth", choices=["basic", "ntlm", "selenium"], default="basic", help="Typ autentizace (basic, ntlm nebo selenium)")
	parser.add_argument("--selenium-headless", action="store_true", help="Spustit Selenium v headless režimu (bez okna prohlížeče)")
	parser.add_argument("--selenium-no-persist", action="store_true", help="Nepoužívat persistentní profil prohlížeče (vždy čisté přihlášení)")


	args = parser.parse_args(argv)

	verify = True
	if args.insecure:
		verify = False
		try:
			import urllib3
			urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
		except Exception:
			pass
		print("POZOR: Ověřování certifikátu vypnuto (verify=False). Používejte pouze, pokud server má neplatný certifikát.")

	# Zapomenout kredenciály a skončit
	if args.forget:
		forget_credentials(username_hint=args.user)
		print("Uložené přihlašovací údaje byly smazány.")
		return 0

	try:
		username, password = get_credentials(username_override=args.user, force_prompt_password=args.ask_password)
		records = fetch_files(
			args.url,
			username,
			password,
			verify=verify,
			debug=args.debug,
			auth_mode=args.auth,
			selenium_headless=args.selenium_headless,
			selenium_persist=not args.selenium_no_persist,
		)
	except Exception as e:
		print(f"Chyba: {e}")
		return 1

	if not records:
		print("Na stránce se nepodařilo najít žádné Excel soubory (.xls/.xlsx).")
	else:
		print(f"Nalezeno {len(records)} záznamů. Generuji report…")

	output_path = Path(args.output).resolve()
	try:
		generate_report(records, output_path)
	except Exception as e:
		print(f"Chyba při generování Excelu: {e}")
		return 1

	print(f"Hotovo. Výstup: {output_path}")
	return 0


if __name__ == "__main__":
	sys.exit(main(sys.argv[1:]))

